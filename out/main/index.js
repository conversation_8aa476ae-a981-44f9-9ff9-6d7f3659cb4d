"use strict";
const electron = require("electron");
const path = require("path");
const SpotifyWebApi = require("spotify-web-api-node");
const Store = require("electron-store");
const http = require("http");
const dotenv = require("dotenv");
const is = {
  dev: !electron.app.isPackaged
};
const platform = {
  isWindows: process.platform === "win32",
  isMacOS: process.platform === "darwin",
  isLinux: process.platform === "linux"
};
const electronApp = {
  setAppUserModelId(id) {
    if (platform.isWindows)
      electron.app.setAppUserModelId(is.dev ? process.execPath : id);
  },
  setAutoLaunch(auto) {
    if (platform.isLinux)
      return false;
    const isOpenAtLogin = () => {
      return electron.app.getLoginItemSettings().openAtLogin;
    };
    if (isOpenAtLogin() !== auto) {
      electron.app.setLoginItemSettings({
        openAtLogin: auto,
        path: process.execPath
      });
      return isOpenAtLogin() === auto;
    } else {
      return true;
    }
  },
  skipProxy() {
    return electron.session.defaultSession.setProxy({ mode: "direct" });
  }
};
const optimizer = {
  watchWindowShortcuts(window, shortcutOptions) {
    if (!window)
      return;
    const { webContents } = window;
    const { escToCloseWindow = false, zoom = false } = shortcutOptions || {};
    webContents.on("before-input-event", (event, input) => {
      if (input.type === "keyDown") {
        if (!is.dev) {
          if (input.code === "KeyR" && (input.control || input.meta))
            event.preventDefault();
        } else {
          if (input.code === "F12") {
            if (webContents.isDevToolsOpened()) {
              webContents.closeDevTools();
            } else {
              webContents.openDevTools({ mode: "undocked" });
              console.log("Open dev tool...");
            }
          }
        }
        if (escToCloseWindow) {
          if (input.code === "Escape" && input.key !== "Process") {
            window.close();
            event.preventDefault();
          }
        }
        if (!zoom) {
          if (input.code === "Minus" && (input.control || input.meta))
            event.preventDefault();
          if (input.code === "Equal" && input.shift && (input.control || input.meta))
            event.preventDefault();
        }
      }
    });
  },
  registerFramelessWindowIpc() {
    electron.ipcMain.on("win:invoke", (event, action) => {
      const win = electron.BrowserWindow.fromWebContents(event.sender);
      if (win) {
        if (action === "show") {
          win.show();
        } else if (action === "showInactive") {
          win.showInactive();
        } else if (action === "min") {
          win.minimize();
        } else if (action === "max") {
          const isMaximized = win.isMaximized();
          if (isMaximized) {
            win.unmaximize();
          } else {
            win.maximize();
          }
        } else if (action === "close") {
          win.close();
        }
      }
    });
  }
};
class CallbackServer {
  server = null;
  port = 8888;
  isRunning = false;
  start() {
    if (this.isRunning) {
      return Promise.resolve();
    }
    return new Promise((resolve, reject) => {
      this.server = http.createServer((req, res) => {
        if (req.url?.startsWith("/callback")) {
          res.writeHead(200, { "Content-Type": "text/html" });
          res.end(`
            <html>
              <head><title>Spotify Authentication</title></head>
              <body>
                <h1>Authentication Successful!</h1>
                <p>You can now close this window and return to the app.</p>
                <script>
                  setTimeout(() => {
                    window.close();
                  }, 2000);
                <\/script>
              </body>
            </html>
          `);
        } else {
          res.writeHead(404);
          res.end("Not found");
        }
      });
      this.server.listen(this.port, "127.0.0.1", () => {
        console.log(`Callback server running on http://127.0.0.1:${this.port}`);
        this.isRunning = true;
        resolve();
      });
      this.server.on("error", (error) => {
        if (error.code === "EADDRINUSE") {
          console.log(`Port ${this.port} already in use, assuming server is already running`);
          this.isRunning = true;
          resolve();
        } else {
          reject(error);
        }
      });
    });
  }
  stop() {
    if (this.server && this.isRunning) {
      this.server.close();
      this.server = null;
      this.isRunning = false;
      console.log(`Callback server stopped`);
    }
  }
  getCallbackUrl() {
    return `http://127.0.0.1:${this.port}/callback`;
  }
}
class SpotifyService {
  authWindow = null;
  spotifyApi;
  store;
  clientId;
  clientSecret;
  redirectUri;
  static callbackServer = null;
  constructor() {
    this.store = new Store();
    if (!SpotifyService.callbackServer) {
      SpotifyService.callbackServer = new CallbackServer();
    }
    this.clientId = process.env.SPOTIFY_CLIENT_ID || "";
    this.clientSecret = process.env.SPOTIFY_CLIENT_SECRET || "";
    this.redirectUri = SpotifyService.callbackServer.getCallbackUrl();
    if (!this.clientId || !this.clientSecret) {
      console.error("Missing Spotify credentials in environment variables");
      console.error("Please set SPOTIFY_CLIENT_ID and SPOTIFY_CLIENT_SECRET in your .env file");
    }
    this.spotifyApi = new SpotifyWebApi({
      clientId: this.clientId,
      clientSecret: this.clientSecret,
      redirectUri: this.redirectUri
    });
    this.loadStoredTokens();
  }
  async authenticate() {
    try {
      if (!this.clientId || !this.clientSecret) {
        return {
          success: false,
          error: "Missing Spotify credentials. Please check your .env file."
        };
      }
      if (await this.hasValidTokens()) {
        const user = await this.getCurrentUser();
        return { success: true, user };
      }
      return await this.startOAuthFlow();
    } catch (error) {
      console.error("Authentication error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Authentication failed"
      };
    }
  }
  async startOAuthFlow() {
    try {
      await SpotifyService.callbackServer.start();
      const scopes = [
        "user-read-private",
        "user-read-email",
        "playlist-read-private",
        "playlist-read-collaborative",
        "streaming",
        "user-read-playback-state",
        "user-modify-playback-state"
      ];
      const authorizeURL = this.spotifyApi.createAuthorizeURL(scopes, "state-key");
      return new Promise((resolve, reject) => {
        if (this.authWindow) {
          this.authWindow.close();
          this.authWindow = null;
        }
        this.authWindow = new electron.BrowserWindow({
          width: 800,
          height: 600,
          show: true,
          title: "Spotify Authentication",
          webPreferences: {
            nodeIntegration: false,
            contextIsolation: true
          }
        });
        this.authWindow.loadURL(authorizeURL);
        this.authWindow.webContents.on("will-navigate", async (event, url) => {
          if (url.startsWith(this.redirectUri)) {
            event.preventDefault();
            const urlParams = new URL(url).searchParams;
            const code = urlParams.get("code");
            const error = urlParams.get("error");
            if (error) {
              this.cleanup();
              reject(new Error(`Spotify OAuth error: ${error}`));
              return;
            }
            if (code) {
              try {
                console.log("Exchanging authorization code for tokens...");
                const data = await this.spotifyApi.authorizationCodeGrant(code);
                this.storeTokens(data.body);
                this.spotifyApi.setAccessToken(data.body.access_token);
                this.spotifyApi.setRefreshToken(data.body.refresh_token);
                console.log("Getting user profile...");
                const user = await this.getCurrentUser();
                console.log("Authentication successful for user:", user.display_name);
                this.cleanup();
                resolve({ success: true, user });
              } catch (err) {
                console.error("Error during token exchange:", err);
                this.cleanup();
                reject(err);
              }
            }
          }
        });
        this.authWindow.on("closed", () => {
          this.authWindow = null;
          this.cleanup();
          reject(new Error("Authentication window was closed"));
        });
        setTimeout(() => {
          if (this.authWindow) {
            this.cleanup();
            reject(new Error("Authentication timeout"));
          }
        }, 3e5);
      });
    } catch (error) {
      this.cleanup();
      throw error;
    }
  }
  cleanup() {
    if (this.authWindow) {
      this.authWindow.close();
      this.authWindow = null;
    }
  }
  async hasValidTokens() {
    const tokens = this.store.get("spotify_tokens");
    if (!tokens) return false;
    const expiryTime = this.store.get("spotify_token_expiry");
    if (Date.now() > expiryTime) {
      return await this.refreshTokens();
    }
    this.spotifyApi.setAccessToken(tokens.access_token);
    this.spotifyApi.setRefreshToken(tokens.refresh_token);
    return true;
  }
  async refreshTokens() {
    try {
      const refreshToken = this.store.get("spotify_refresh_token");
      if (!refreshToken) return false;
      this.spotifyApi.setRefreshToken(refreshToken);
      const data = await this.spotifyApi.refreshAccessToken();
      this.storeTokens(data.body);
      this.spotifyApi.setAccessToken(data.body.access_token);
      return true;
    } catch (error) {
      console.error("Token refresh error:", error);
      return false;
    }
  }
  storeTokens(tokens) {
    this.store.set("spotify_tokens", tokens);
    this.store.set("spotify_token_expiry", Date.now() + tokens.expires_in * 1e3);
    if (tokens.refresh_token) {
      this.store.set("spotify_refresh_token", tokens.refresh_token);
    }
  }
  loadStoredTokens() {
    const tokens = this.store.get("spotify_tokens");
    if (tokens) {
      this.spotifyApi.setAccessToken(tokens.access_token);
      const refreshToken = this.store.get("spotify_refresh_token");
      if (refreshToken) {
        this.spotifyApi.setRefreshToken(refreshToken);
      }
    }
  }
  async getCurrentUser() {
    try {
      const data = await this.spotifyApi.getMe();
      return data.body;
    } catch (error) {
      console.error("Error fetching user:", error);
      throw error;
    }
  }
  async getPlaylists() {
    try {
      const data = await this.spotifyApi.getUserPlaylists();
      return data.body.items;
    } catch (error) {
      console.error("Error fetching playlists:", error);
      throw error;
    }
  }
  async getPlaylistTracks(playlistId) {
    try {
      const data = await this.spotifyApi.getPlaylistTracks(playlistId);
      return data.body.items;
    } catch (error) {
      console.error("Error fetching playlist tracks:", error);
      throw error;
    }
  }
  async playTrack(trackUri) {
    console.log("Playing track:", trackUri);
  }
  async pauseTrack() {
    console.log("Pausing track");
  }
  async resumeTrack() {
    console.log("Resuming track");
  }
  async seekTrack(position) {
    console.log("Seeking to position:", position);
  }
  async setVolume(volume) {
    console.log("Setting volume to:", volume);
  }
  async getCurrentState() {
    return {
      is_playing: false,
      progress_ms: 0,
      item: null
    };
  }
  async logout() {
    console.log("Logging out");
    this.store.delete("spotify_tokens");
    this.store.delete("spotify_token_expiry");
    this.store.delete("spotify_refresh_token");
    this.spotifyApi.resetAccessToken();
    this.spotifyApi.resetRefreshToken();
  }
}
function registerSpotifyHandlers() {
  const spotifyService = new SpotifyService();
  electron.ipcMain.handle("spotify:authenticate", async () => {
    return await spotifyService.authenticate();
  });
  electron.ipcMain.handle("spotify:getPlaylists", async () => {
    return await spotifyService.getPlaylists();
  });
  electron.ipcMain.handle("spotify:getPlaylistTracks", async (_, playlistId) => {
    return await spotifyService.getPlaylistTracks(playlistId);
  });
  electron.ipcMain.handle("spotify:playTrack", async (_, trackUri) => {
    return await spotifyService.playTrack(trackUri);
  });
  electron.ipcMain.handle("spotify:pauseTrack", async () => {
    return await spotifyService.pauseTrack();
  });
  electron.ipcMain.handle("spotify:resumeTrack", async () => {
    return await spotifyService.resumeTrack();
  });
  electron.ipcMain.handle("spotify:seekTrack", async (_, position) => {
    return await spotifyService.seekTrack(position);
  });
  electron.ipcMain.handle("spotify:setVolume", async (_, volume) => {
    return await spotifyService.setVolume(volume);
  });
  electron.ipcMain.handle("spotify:getCurrentState", async () => {
    return await spotifyService.getCurrentState();
  });
  electron.ipcMain.handle("spotify:logout", async () => {
    return await spotifyService.logout();
  });
}
dotenv.config();
function createWindow() {
  const mainWindow = new electron.BrowserWindow({
    width: 1200,
    height: 800,
    show: false,
    autoHideMenuBar: true,
    ...process.platform === "linux" ? {} : {},
    webPreferences: {
      preload: path.join(__dirname, "../preload/index.js"),
      sandbox: false,
      contextIsolation: true,
      enableRemoteModule: false,
      nodeIntegration: false
    }
  });
  mainWindow.on("ready-to-show", () => {
    mainWindow.show();
  });
  mainWindow.webContents.setWindowOpenHandler((details) => {
    electron.shell.openExternal(details.url);
    return { action: "deny" };
  });
  if (is.dev && process.env["ELECTRON_RENDERER_URL"]) {
    mainWindow.loadURL(process.env["ELECTRON_RENDERER_URL"]);
  } else {
    mainWindow.loadFile(path.join(__dirname, "../renderer/index.html"));
  }
}
electron.app.whenReady().then(() => {
  electronApp.setAppUserModelId("com.odmustafa.spotify-soulseek");
  electron.app.on("browser-window-created", (_, window) => {
    optimizer.watchWindowShortcuts(window);
  });
  electron.ipcMain.handle("ping", () => "pong");
  registerSpotifyHandlers();
  createWindow();
  electron.app.on("activate", function() {
    if (electron.BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    electron.app.quit();
  }
});
