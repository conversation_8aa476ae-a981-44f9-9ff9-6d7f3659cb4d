import { ipcMain } from 'electron'
import { SpotifyService } from '../services/spotify-service'

export function registerSpotifyHandlers(): void {
  const spotifyService = new SpotifyService()

  ipcMain.handle('spotify:authenticate', async () => {
    return await spotifyService.authenticate()
  })

  ipcMain.handle('spotify:getPlaylists', async () => {
    return await spotifyService.getPlaylists()
  })

  ipcMain.handle('spotify:getPlaylistTracks', async (_, playlistId: string) => {
    return await spotifyService.getPlaylistTracks(playlistId)
  })

  ipcMain.handle('spotify:playTrack', async (_, trackUri: string) => {
    return await spotifyService.playTrack(trackUri)
  })

  ipcMain.handle('spotify:pauseTrack', async () => {
    return await spotifyService.pauseTrack()
  })

  ipcMain.handle('spotify:resumeTrack', async () => {
    return await spotifyService.resumeTrack()
  })

  ipcMain.handle('spotify:seekTrack', async (_, position: number) => {
    return await spotifyService.seekTrack(position)
  })

  ipcMain.handle('spotify:setVolume', async (_, volume: number) => {
    return await spotifyService.setVolume(volume)
  })

  ipcMain.handle('spotify:getCurrentState', async () => {
    return await spotifyService.getCurrentState()
  })

  ipcMain.handle('spotify:logout', async () => {
    return await spotifyService.logout()
  })
}
