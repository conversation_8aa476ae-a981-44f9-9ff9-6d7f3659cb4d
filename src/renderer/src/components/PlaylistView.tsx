import { Music, Play, Clock, User } from 'lucide-react'

interface SpotifyPlaylist {
  id: string
  name: string
  description: string
  images: Array<{ url: string }>
  tracks: { total: number }
  owner: { display_name: string }
}

interface SpotifyTrack {
  track: {
    id: string
    name: string
    artists: Array<{ name: string }>
    album: {
      name: string
      images: Array<{ url: string }>
    }
    duration_ms: number
    preview_url?: string
  }
}

interface PlaylistViewProps {
  playlists: SpotifyPlaylist[]
  selectedPlaylist: SpotifyPlaylist | null
  tracks: SpotifyTrack[]
  loading: boolean
  onSelectPlaylist: (playlist: SpotifyPlaylist) => void
  onPlayTrack: (trackUri: string) => void
}

export function PlaylistView({ 
  playlists, 
  selectedPlaylist, 
  tracks, 
  loading, 
  onSelectPlaylist, 
  onPlayTrack 
}: PlaylistViewProps) {
  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000)
    const seconds = Math.floor((ms % 60000) / 1000)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  if (loading && playlists.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading playlists...</p>
        </div>
      </div>
    )
  }

  if (!selectedPlaylist) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {playlists.map((playlist) => (
          <div
            key={playlist.id}
            onClick={() => onSelectPlaylist(playlist)}
            className="bg-white rounded-lg shadow-sm border p-4 hover:shadow-md transition-shadow cursor-pointer"
          >
            <div className="flex items-start space-x-3">
              <div className="w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center flex-shrink-0">
                {playlist.images.length > 0 ? (
                  <img
                    src={playlist.images[0].url}
                    alt={playlist.name}
                    className="w-full h-full object-cover rounded-md"
                  />
                ) : (
                  <Music className="w-8 h-8 text-gray-400" />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-gray-900 truncate">{playlist.name}</h3>
                <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                  {playlist.description || 'No description'}
                </p>
                <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                  <span className="flex items-center">
                    <Music className="w-3 h-3 mr-1" />
                    {playlist.tracks.total} tracks
                  </span>
                  <span className="flex items-center">
                    <User className="w-3 h-3 mr-1" />
                    {playlist.owner.display_name}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Playlist Header */}
      <div className="flex items-start space-x-4 pb-6 border-b">
        <div className="w-32 h-32 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
          {selectedPlaylist.images.length > 0 ? (
            <img
              src={selectedPlaylist.images[0].url}
              alt={selectedPlaylist.name}
              className="w-full h-full object-cover rounded-lg"
            />
          ) : (
            <Music className="w-16 h-16 text-gray-400" />
          )}
        </div>
        <div className="flex-1">
          <button
            onClick={() => onSelectPlaylist(null as any)}
            className="text-sm text-green-600 hover:text-green-700 mb-2"
          >
            ← Back to playlists
          </button>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">{selectedPlaylist.name}</h1>
          <p className="text-gray-600 mb-4">
            {selectedPlaylist.description || 'No description'}
          </p>
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span>{selectedPlaylist.tracks.total} tracks</span>
            <span>By {selectedPlaylist.owner.display_name}</span>
          </div>
        </div>
      </div>

      {/* Track List */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading tracks...</p>
          </div>
        </div>
      ) : (
        <div className="space-y-2">
          {tracks.map((item, index) => (
            <div
              key={item.track.id}
              className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 group"
            >
              <div className="w-8 text-center text-sm text-gray-500 group-hover:hidden">
                {index + 1}
              </div>
              <button
                onClick={() => onPlayTrack(`spotify:track:${item.track.id}`)}
                className="w-8 h-8 hidden group-hover:flex items-center justify-center bg-green-500 text-white rounded-full hover:bg-green-600"
                disabled={!item.track.preview_url}
              >
                <Play className="w-4 h-4" />
              </button>
              
              <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center flex-shrink-0">
                {item.track.album.images.length > 0 ? (
                  <img
                    src={item.track.album.images[item.track.album.images.length - 1].url}
                    alt={item.track.album.name}
                    className="w-full h-full object-cover rounded"
                  />
                ) : (
                  <Music className="w-6 h-6 text-gray-400" />
                )}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="font-medium text-gray-900 truncate">{item.track.name}</div>
                <div className="text-sm text-gray-600 truncate">
                  {item.track.artists.map(artist => artist.name).join(', ')}
                </div>
              </div>
              
              <div className="hidden md:block text-sm text-gray-600 truncate max-w-xs">
                {item.track.album.name}
              </div>
              
              <div className="flex items-center text-sm text-gray-500">
                <Clock className="w-4 h-4 mr-1" />
                {formatDuration(item.track.duration_ms)}
              </div>
              
              <button className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600">
                Search Soulseek
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
